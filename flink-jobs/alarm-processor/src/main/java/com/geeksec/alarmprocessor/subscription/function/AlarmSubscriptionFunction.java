package com.geeksec.alarmprocessor.subscription.function;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.subscription.manager.SubscriptionManager;
import com.geeksec.alarmprocessor.subscription.model.AlarmSubscription;
import com.geeksec.alarmprocessor.subscription.model.NotificationChannel;
import com.geeksec.alarmprocessor.subscription.notification.NotificationSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 告警订阅处理函数
 * 负责处理告警的订阅匹配和通知发送
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlarmSubscriptionFunction extends RichFlatMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 配置对象
     */
    private final AlarmProcessorConfig config;
    
    /**
     * 是否启用订阅功能
     */
    private final boolean subscriptionEnabled;
    
    /**
     * 是否透传原始告警
     */
    private final boolean passthroughAlarms;
    
    /**
     * 统计输出间隔（秒）
     */
    private final int statisticsIntervalSeconds;
    
    /**
     * 配置刷新间隔（秒）
     */
    private final int configRefreshIntervalSeconds;
    
    /**
     * 订阅管理器
     */
    private transient SubscriptionManager subscriptionManager;
    
    /**
     * 定时任务执行器
     */
    private transient ScheduledExecutorService scheduledExecutor;
    
    /**
     * 处理计数器
     */
    private volatile long processedCount = 0;
    private volatile long matchedCount = 0;
    private volatile long notifiedCount = 0;
    private volatile long failedCount = 0;
    
    /**
     * 构造函数
     * 
     * @param config 配置对象
     */
    public AlarmSubscriptionFunction(AlarmProcessorConfig config) {
        this.config = config;
        this.subscriptionEnabled = config.isSubscriptionEnabled();
        this.passthroughAlarms = config.isSubscriptionPassthroughAlarms();
        this.statisticsIntervalSeconds = config.getSubscriptionStatisticsIntervalSeconds();
        this.configRefreshIntervalSeconds = config.getSubscriptionConfigRefreshIntervalSeconds();
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        if (!subscriptionEnabled) {
            log.info("告警订阅功能已禁用");
            return;
        }
        
        // 初始化通知发送器
        NotificationSender notificationSender = new NotificationSender();
        
        // 初始化订阅管理器
        subscriptionManager = new SubscriptionManager(notificationSender);
        
        // 加载初始订阅配置
        loadSubscriptionConfig();
        
        // 启动定时任务
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "AlarmSubscription-Timer");
            t.setDaemon(true);
            return t;
        });
        
        // 定时输出统计信息
        if (statisticsIntervalSeconds > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::printStatistics,
                    statisticsIntervalSeconds,
                    statisticsIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        // 定时刷新配置
        if (configRefreshIntervalSeconds > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::refreshSubscriptionConfig,
                    configRefreshIntervalSeconds,
                    configRefreshIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        log.info("告警订阅函数初始化完成，订阅处理: {}, 透传告警: {}", 
                subscriptionEnabled, passthroughAlarms);
    }
    
    @Override
    public void flatMap(Alarm alarm, Collector<Alarm> out) throws Exception {
        if (alarm == null) {
            return;
        }
        
        processedCount++;
        
        // 如果启用透传，直接输出原始告警
        if (passthroughAlarms) {
            out.collect(alarm);
        }
        
        // 如果启用订阅处理，进行订阅匹配和通知
        if (subscriptionEnabled && subscriptionManager != null) {
            try {
                SubscriptionManager.SubscriptionProcessResult result = 
                        subscriptionManager.processAlarm(alarm);
                
                if (result.isSuccess()) {
                    matchedCount += result.getMatchedCount();
                    
                    // 统计通知结果
                    for (SubscriptionManager.NotificationResult notificationResult : result.getNotificationResults()) {
                        if (notificationResult.isSuccess()) {
                            notifiedCount++;
                        } else {
                            failedCount++;
                        }
                    }
                    
                    log.debug("告警订阅处理成功: 告警ID={}, 匹配订阅数={}, 通知结果数={}", 
                            alarm.getAlarmId(), result.getMatchedCount(), result.getNotificationResults().size());
                } else {
                    failedCount++;
                    log.warn("告警订阅处理失败: 告警ID={}, 错误={}", alarm.getAlarmId(), result.getMessage());
                }
                
            } catch (Exception e) {
                failedCount++;
                log.error("处理告警订阅时发生异常: 告警ID={}, 错误={}", alarm.getAlarmId(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * 加载订阅配置
     */
    private void loadSubscriptionConfig() {
        try {
            log.info("开始加载订阅配置");
            
            // 创建示例订阅配置
            createExampleSubscriptions();
            
            log.info("订阅配置加载完成");
            
        } catch (Exception e) {
            log.error("加载订阅配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建示例订阅配置
     */
    private void createExampleSubscriptions() {
        try {
            log.info("创建示例订阅配置");
            
            // 创建高危告警订阅
            AlarmSubscription highRiskSubscription = AlarmSubscription.builder()
                    .subscriptionId("high-risk-subscription")
                    .userId("admin")
                    .username("管理员")
                    .subscriptionName("高危告警订阅")
                    .description("订阅所有高危级别的告警")
                    .enabled(true)
                    .priority(AlarmSubscription.SubscriptionPriority.HIGH)
                    .build();
            
            // 添加规则：告警级别为高危
            com.geeksec.alarmprocessor.subscription.model.SubscriptionRule highRiskRule = 
                    com.geeksec.alarmprocessor.subscription.model.SubscriptionRule.createFieldRule(
                            "alarmLevel", 
                            com.geeksec.alarmprocessor.subscription.model.SubscriptionRule.Operator.EQUALS, 
                            "HIGH");
            highRiskSubscription.setRules(java.util.List.of(highRiskRule));
            
            // 添加邮件通知渠道
            NotificationChannel emailChannel = NotificationChannel.createEmailChannel(
                    "<EMAIL>", "default_email");
            highRiskSubscription.setChannels(java.util.List.of(emailChannel));
            
            subscriptionManager.addSubscription(highRiskSubscription);
            
            // 创建恶意软件告警订阅
            AlarmSubscription malwareSubscription = AlarmSubscription.builder()
                    .subscriptionId("malware-subscription")
                    .userId("security")
                    .username("安全团队")
                    .subscriptionName("恶意软件告警订阅")
                    .description("订阅所有恶意软件相关的告警")
                    .enabled(true)
                    .priority(AlarmSubscription.SubscriptionPriority.URGENT)
                    .build();
            
            // 添加规则：威胁类型包含恶意软件
            com.geeksec.alarmprocessor.subscription.model.SubscriptionRule malwareRule = 
                    com.geeksec.alarmprocessor.subscription.model.SubscriptionRule.createContainsRule(
                            "threatType", "恶意软件");
            malwareSubscription.setRules(java.util.List.of(malwareRule));
            
            // 添加短信通知渠道
            NotificationChannel smsChannel = NotificationChannel.createSmsChannel(
                    "13800138000", "default_sms");
            malwareSubscription.setChannels(java.util.List.of(smsChannel));
            
            subscriptionManager.addSubscription(malwareSubscription);
            
            log.info("创建示例订阅完成");
            
        } catch (Exception e) {
            log.error("创建示例订阅失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 刷新订阅配置
     */
    private void refreshSubscriptionConfig() {
        try {
            log.debug("刷新订阅配置");
            
            // 这里可以实现从外部配置源重新加载订阅配置的逻辑
            // 比如从数据库、配置文件或配置中心重新加载
            
            // 执行清理
            if (subscriptionManager != null) {
                subscriptionManager.cleanup();
            }
            
        } catch (Exception e) {
            log.error("刷新订阅配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 打印统计信息
     */
    private void printStatistics() {
        try {
            log.info("=== 告警订阅统计信息 ===");
            log.info("处理告警数: {}", processedCount);
            log.info("匹配订阅数: {}", matchedCount);
            log.info("成功通知数: {}", notifiedCount);
            log.info("失败通知数: {}", failedCount);
            
            if (processedCount > 0) {
                double matchRate = (double) matchedCount / processedCount * 100;
                log.info("匹配率: {:.2f}%", matchRate);
            }
            
            if (matchedCount > 0) {
                double successRate = (double) notifiedCount / (notifiedCount + failedCount) * 100;
                log.info("通知成功率: {:.2f}%", successRate);
            }
            
            if (subscriptionManager != null) {
                SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
                log.info("订阅管理器统计:");
                log.info("  总订阅数: {}", stats.totalSubscriptions());
                log.info("  总用户数: {}", stats.totalUsers());
                log.info("  总模板数: {}", stats.totalTemplates());
                log.info("  总处理数: {}", stats.totalProcessed());
                log.info("  总匹配数: {}", stats.totalMatched());
                log.info("  总通知数: {}", stats.totalNotified());
                log.info("  总失败数: {}", stats.totalFailed());
                log.info("  匹配率: {:.2f}%", stats.getMatchRate() * 100);
                log.info("  通知成功率: {:.2f}%", stats.getNotificationSuccessRate() * 100);
            }
            
            log.info("=== 统计信息完成 ===");
            
        } catch (Exception e) {
            log.error("打印统计信息失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void close() throws Exception {
        try {
            log.info("关闭告警订阅函数");
            
            // 关闭定时任务
            if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
                scheduledExecutor.shutdown();
                try {
                    if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduledExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduledExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            // 打印最终统计信息
            printFinalStatistics();
            
        } finally {
            super.close();
        }
    }
    
    /**
     * 打印最终统计信息
     */
    private void printFinalStatistics() {
        log.info("=== 告警订阅最终统计 ===");
        log.info("总处理告警数: {}", processedCount);
        log.info("总匹配订阅数: {}", matchedCount);
        log.info("总成功通知数: {}", notifiedCount);
        log.info("总失败通知数: {}", failedCount);
        
        if (processedCount > 0) {
            double matchRate = (double) matchedCount / processedCount * 100;
            log.info("总匹配率: {:.2f}%", matchRate);
        }
        
        if (matchedCount > 0) {
            double successRate = (double) notifiedCount / (notifiedCount + failedCount) * 100;
            log.info("总通知成功率: {:.2f}%", successRate);
        }
        
        if (subscriptionManager != null) {
            SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
            log.info("订阅管理器最终统计:");
            log.info("  总订阅数: {}", stats.totalSubscriptions());
            log.info("  总用户数: {}", stats.totalUsers());
            log.info("  总模板数: {}", stats.totalTemplates());
            log.info("  总处理数: {}", stats.totalProcessed());
            log.info("  总匹配数: {}", stats.totalMatched());
            log.info("  总通知数: {}", stats.totalNotified());
            log.info("  总失败数: {}", stats.totalFailed());
            log.info("  匹配率: {:.2f}%", stats.getMatchRate() * 100);
            log.info("  通知成功率: {:.2f}%", stats.getNotificationSuccessRate() * 100);
        }
        
        log.info("=== 最终统计完成 ===");
    }
}
