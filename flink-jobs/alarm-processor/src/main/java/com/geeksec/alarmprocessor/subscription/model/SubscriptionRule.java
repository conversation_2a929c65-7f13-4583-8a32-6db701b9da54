package com.geeksec.alarmprocessor.subscription.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 订阅规则模型
 * 定义告警匹配的具体规则
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRule implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型
     */
    private RuleType ruleType;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 操作符
     */
    private Operator operator;
    
    /**
     * 匹配值
     */
    private Object value;
    
    /**
     * 匹配值列表（用于IN、NOT_IN操作）
     */
    private List<Object> values;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 是否区分大小写（用于字符串匹配）
     */
    private Boolean caseSensitive;
    
    /**
     * 正则表达式模式（缓存编译后的Pattern）
     */
    private transient Pattern regexPattern;
    
    /**
     * 规则权重
     */
    private Integer weight;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 规则类型枚举
     */
    public enum RuleType {
        /** 字段匹配规则 */
        FIELD_MATCH,
        /** 复合条件规则 */
        COMPOSITE,
        /** 自定义脚本规则 */
        SCRIPT,
        /** 时间范围规则 */
        TIME_RANGE,
        /** 地理位置规则 */
        GEO_LOCATION
    }
    
    /**
     * 操作符枚举
     */
    public enum Operator {
        /** 等于 */
        EQUALS,
        /** 不等于 */
        NOT_EQUALS,
        /** 包含 */
        CONTAINS,
        /** 不包含 */
        NOT_CONTAINS,
        /** 开始于 */
        STARTS_WITH,
        /** 结束于 */
        ENDS_WITH,
        /** 正则匹配 */
        REGEX,
        /** 大于 */
        GREATER_THAN,
        /** 大于等于 */
        GREATER_THAN_OR_EQUAL,
        /** 小于 */
        LESS_THAN,
        /** 小于等于 */
        LESS_THAN_OR_EQUAL,
        /** 在列表中 */
        IN,
        /** 不在列表中 */
        NOT_IN,
        /** 为空 */
        IS_NULL,
        /** 不为空 */
        IS_NOT_NULL,
        /** 在范围内 */
        BETWEEN,
        /** 不在范围内 */
        NOT_BETWEEN
    }
    
    /**
     * 创建字段匹配规则
     * 
     * @param fieldName 字段名称
     * @param operator 操作符
     * @param value 匹配值
     * @return 规则实例
     */
    public static SubscriptionRule createFieldRule(String fieldName, Operator operator, Object value) {
        return SubscriptionRule.builder()
                .ruleType(RuleType.FIELD_MATCH)
                .fieldName(fieldName)
                .operator(operator)
                .value(value)
                .enabled(true)
                .caseSensitive(false)
                .weight(1)
                .build();
    }
    
    /**
     * 创建包含规则
     * 
     * @param fieldName 字段名称
     * @param value 包含的值
     * @return 规则实例
     */
    public static SubscriptionRule createContainsRule(String fieldName, String value) {
        return createFieldRule(fieldName, Operator.CONTAINS, value);
    }
    
    /**
     * 创建等于规则
     * 
     * @param fieldName 字段名称
     * @param value 等于的值
     * @return 规则实例
     */
    public static SubscriptionRule createEqualsRule(String fieldName, Object value) {
        return createFieldRule(fieldName, Operator.EQUALS, value);
    }
    
    /**
     * 创建正则匹配规则
     * 
     * @param fieldName 字段名称
     * @param regex 正则表达式
     * @return 规则实例
     */
    public static SubscriptionRule createRegexRule(String fieldName, String regex) {
        return createFieldRule(fieldName, Operator.REGEX, regex);
    }
    
    /**
     * 创建IN规则
     * 
     * @param fieldName 字段名称
     * @param values 值列表
     * @return 规则实例
     */
    public static SubscriptionRule createInRule(String fieldName, List<Object> values) {
        return SubscriptionRule.builder()
                .ruleType(RuleType.FIELD_MATCH)
                .fieldName(fieldName)
                .operator(Operator.IN)
                .values(values)
                .enabled(true)
                .weight(1)
                .build();
    }
    
    /**
     * 验证规则配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (!Boolean.TRUE.equals(enabled)) {
            return false;
        }
        
        if (ruleType == null || operator == null) {
            return false;
        }
        
        if (ruleType == RuleType.FIELD_MATCH && 
            (fieldName == null || fieldName.trim().isEmpty())) {
            return false;
        }
        
        // 检查操作符和值的匹配性
        switch (operator) {
            case IN, NOT_IN -> {
                if (values == null || values.isEmpty()) {
                    return false;
                }
            }
            case IS_NULL, IS_NOT_NULL -> {
                // 这些操作符不需要值
            }
            default -> {
                if (value == null) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 检查规则是否匹配给定的对象
     * 
     * @param target 目标对象
     * @return 是否匹配
     */
    public boolean matches(Object target) {
        if (!isValid() || target == null) {
            return false;
        }
        
        try {
            switch (ruleType) {
                case FIELD_MATCH -> {
                    return matchesField(target);
                }
                case COMPOSITE -> {
                    return matchesComposite(target);
                }
                case SCRIPT -> {
                    return matchesScript(target);
                }
                case TIME_RANGE -> {
                    return matchesTimeRange(target);
                }
                case GEO_LOCATION -> {
                    return matchesGeoLocation(target);
                }
                default -> {
                    return false;
                }
            }
        } catch (Exception e) {
            // 匹配过程中出现异常，记录日志并返回false
            return false;
        }
    }
    
    /**
     * 字段匹配
     */
    private boolean matchesField(Object target) {
        Object fieldValue = extractFieldValue(target, fieldName);
        
        switch (operator) {
            case EQUALS -> {
                return objectEquals(fieldValue, value);
            }
            case NOT_EQUALS -> {
                return !objectEquals(fieldValue, value);
            }
            case CONTAINS -> {
                return stringContains(fieldValue, value);
            }
            case NOT_CONTAINS -> {
                return !stringContains(fieldValue, value);
            }
            case STARTS_WITH -> {
                return stringStartsWith(fieldValue, value);
            }
            case ENDS_WITH -> {
                return stringEndsWith(fieldValue, value);
            }
            case REGEX -> {
                return regexMatches(fieldValue, value);
            }
            case GREATER_THAN -> {
                return numberCompare(fieldValue, value) > 0;
            }
            case GREATER_THAN_OR_EQUAL -> {
                return numberCompare(fieldValue, value) >= 0;
            }
            case LESS_THAN -> {
                return numberCompare(fieldValue, value) < 0;
            }
            case LESS_THAN_OR_EQUAL -> {
                return numberCompare(fieldValue, value) <= 0;
            }
            case IN -> {
                return listContains(values, fieldValue);
            }
            case NOT_IN -> {
                return !listContains(values, fieldValue);
            }
            case IS_NULL -> {
                return fieldValue == null;
            }
            case IS_NOT_NULL -> {
                return fieldValue != null;
            }
            default -> {
                return false;
            }
        }
    }
    
    /**
     * 复合条件匹配（暂时简化实现）
     */
    private boolean matchesComposite(Object target) {
        // 这里应该实现复合条件的逻辑
        return false;
    }
    
    /**
     * 脚本匹配（暂时简化实现）
     */
    private boolean matchesScript(Object target) {
        // 这里应该实现脚本执行的逻辑
        return false;
    }
    
    /**
     * 时间范围匹配（暂时简化实现）
     */
    private boolean matchesTimeRange(Object target) {
        // 这里应该实现时间范围匹配的逻辑
        return false;
    }
    
    /**
     * 地理位置匹配（暂时简化实现）
     */
    private boolean matchesGeoLocation(Object target) {
        // 这里应该实现地理位置匹配的逻辑
        return false;
    }
    
    /**
     * 从对象中提取字段值
     */
    private Object extractFieldValue(Object target, String fieldName) {
        if (target == null || fieldName == null) {
            return null;
        }

        try {
            // 如果是Map类型
            if (target instanceof Map<?, ?> map) {
                return map.get(fieldName);
            }

            // 使用反射获取字段值
            try {
                java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(target);
            } catch (NoSuchFieldException e) {
                // 尝试使用getter方法
                try {
                    String getterName = "get" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
                    java.lang.reflect.Method getter = target.getClass().getMethod(getterName);
                    return getter.invoke(target);
                } catch (Exception ex) {
                    // 如果是枚举类型，尝试转换为字符串
                    if (fieldName.equals("alarmLevel") && target.getClass().getSimpleName().equals("Alarm")) {
                        try {
                            java.lang.reflect.Method method = target.getClass().getMethod("getAlarmLevel");
                            Object result = method.invoke(target);
                            return result != null ? result.toString() : null;
                        } catch (Exception enumEx) {
                            // 忽略
                        }
                    }
                    return null;
                }
            }

        } catch (Exception e) {
            // 字段不存在或访问失败
            return null;
        }
    }
    
    /**
     * 对象相等比较
     */
    private boolean objectEquals(Object a, Object b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        
        // 字符串比较考虑大小写
        if (a instanceof String && b instanceof String) {
            String strA = (String) a;
            String strB = (String) b;
            return Boolean.TRUE.equals(caseSensitive) ? 
                   strA.equals(strB) : strA.equalsIgnoreCase(strB);
        }
        
        return a.equals(b);
    }
    
    /**
     * 字符串包含检查
     */
    private boolean stringContains(Object fieldValue, Object value) {
        if (fieldValue == null || value == null) {
            return false;
        }
        
        String str = fieldValue.toString();
        String pattern = value.toString();
        
        return Boolean.TRUE.equals(caseSensitive) ? 
               str.contains(pattern) : str.toLowerCase().contains(pattern.toLowerCase());
    }
    
    /**
     * 字符串开始检查
     */
    private boolean stringStartsWith(Object fieldValue, Object value) {
        if (fieldValue == null || value == null) {
            return false;
        }
        
        String str = fieldValue.toString();
        String pattern = value.toString();
        
        return Boolean.TRUE.equals(caseSensitive) ? 
               str.startsWith(pattern) : str.toLowerCase().startsWith(pattern.toLowerCase());
    }
    
    /**
     * 字符串结束检查
     */
    private boolean stringEndsWith(Object fieldValue, Object value) {
        if (fieldValue == null || value == null) {
            return false;
        }
        
        String str = fieldValue.toString();
        String pattern = value.toString();
        
        return Boolean.TRUE.equals(caseSensitive) ? 
               str.endsWith(pattern) : str.toLowerCase().endsWith(pattern.toLowerCase());
    }
    
    /**
     * 正则匹配
     */
    private boolean regexMatches(Object fieldValue, Object value) {
        if (fieldValue == null || value == null) {
            return false;
        }
        
        String str = fieldValue.toString();
        String regex = value.toString();
        
        try {
            if (regexPattern == null) {
                int flags = Boolean.TRUE.equals(caseSensitive) ? 0 : Pattern.CASE_INSENSITIVE;
                regexPattern = Pattern.compile(regex, flags);
            }
            return regexPattern.matcher(str).matches();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 数值比较
     */
    private int numberCompare(Object fieldValue, Object value) {
        if (fieldValue == null || value == null) {
            return 0;
        }
        
        try {
            double a = Double.parseDouble(fieldValue.toString());
            double b = Double.parseDouble(value.toString());
            return Double.compare(a, b);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 列表包含检查
     */
    private boolean listContains(List<Object> list, Object value) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        
        for (Object item : list) {
            if (objectEquals(item, value)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取规则的显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (ruleName != null && !ruleName.trim().isEmpty()) {
            return ruleName;
        }
        return String.format("%s %s %s", fieldName, operator, value);
    }
    
    /**
     * 获取规则的简要描述
     * 
     * @return 简要描述
     */
    public String getBriefDescription() {
        if (description != null && !description.trim().isEmpty()) {
            return description.length() > 50 ? description.substring(0, 50) + "..." : description;
        }
        return getDisplayName();
    }
}
