package com.geeksec.alarmprocessor.subscription.manager;

import com.geeksec.alarmprocessor.subscription.model.AlarmSubscription;
import com.geeksec.alarmprocessor.subscription.model.NotificationChannel;
import com.geeksec.alarmprocessor.subscription.model.NotificationTemplate;
import com.geeksec.alarmprocessor.subscription.notification.NotificationSender;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订阅管理器
 * 管理告警订阅的核心组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class SubscriptionManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅缓存
     */
    private final ConcurrentMap<String, AlarmSubscription> subscriptions;
    
    /**
     * 用户订阅索引
     */
    private final ConcurrentMap<String, List<String>> userSubscriptionIndex;
    
    /**
     * 模板缓存
     */
    private final ConcurrentMap<String, NotificationTemplate> templates;
    
    /**
     * 通知发送器
     */
    private final NotificationSender notificationSender;
    
    /**
     * 统计计数器
     */
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalMatched = new AtomicLong(0);
    private final AtomicLong totalNotified = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    
    /**
     * 最后清理时间
     */
    private volatile LocalDateTime lastCleanupTime;
    
    /**
     * 构造函数
     * 
     * @param notificationSender 通知发送器
     */
    public SubscriptionManager(NotificationSender notificationSender) {
        this.subscriptions = new ConcurrentHashMap<>();
        this.userSubscriptionIndex = new ConcurrentHashMap<>();
        this.templates = new ConcurrentHashMap<>();
        this.notificationSender = notificationSender;
        this.lastCleanupTime = LocalDateTime.now();
        
        // 初始化默认模板
        initializeDefaultTemplates();
        
        log.info("订阅管理器初始化完成");
    }
    
    /**
     * 初始化默认模板
     */
    private void initializeDefaultTemplates() {
        // 为每种渠道类型创建默认模板
        for (NotificationChannel.ChannelType channelType : NotificationChannel.ChannelType.values()) {
            NotificationTemplate template = NotificationTemplate.createDefaultAlarmTemplate(channelType);
            templates.put(template.getTemplateId(), template);
        }
        
        log.info("初始化默认模板完成，共 {} 个模板", templates.size());
    }
    
    /**
     * 处理告警
     * 
     * @param alarm 告警对象
     * @return 处理结果
     */
    public SubscriptionProcessResult processAlarm(Object alarm) {
        if (alarm == null) {
            return SubscriptionProcessResult.failure("告警对象为空");
        }
        
        totalProcessed.incrementAndGet();
        
        try {
            // 1. 查找匹配的订阅
            List<AlarmSubscription> matchedSubscriptions = findMatchingSubscriptions(alarm);
            
            if (matchedSubscriptions.isEmpty()) {
                log.debug("未找到匹配的订阅");
                return SubscriptionProcessResult.success(0, List.of());
            }
            
            totalMatched.incrementAndGet();
            
            List<NotificationResult> notificationResults = new ArrayList<>();
            
            for (AlarmSubscription subscription : matchedSubscriptions) {
                try {
                    NotificationResult result = sendNotification(subscription, alarm);
                    notificationResults.add(result);
                    
                    if (result.isSuccess()) {
                        totalNotified.incrementAndGet();
                        subscription.updateTriggerInfo();
                    } else {
                        totalFailed.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    log.error("发送通知失败，订阅ID: {}, 错误: {}", 
                            subscription.getSubscriptionId(), e.getMessage(), e);
                    totalFailed.incrementAndGet();
                    
                    notificationResults.add(NotificationResult.failure(
                            subscription.getSubscriptionId(), e.getMessage()));
                }
            }
            
            return SubscriptionProcessResult.success(matchedSubscriptions.size(), notificationResults);
            
        } catch (Exception e) {
            log.error("处理告警订阅时发生异常: {}", e.getMessage(), e);
            totalFailed.incrementAndGet();
            return SubscriptionProcessResult.failure("处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 查找匹配的订阅
     * 
     * @param alarm 告警对象
     * @return 匹配的订阅列表
     */
    private List<AlarmSubscription> findMatchingSubscriptions(Object alarm) {
        List<AlarmSubscription> matched = new ArrayList<>();
        
        for (AlarmSubscription subscription : subscriptions.values()) {
            if (subscription.isValid() && subscription.matches(alarm)) {
                // 检查是否应该发送通知（考虑免打扰时间、频率控制等）
                boolean isUrgent = subscription.isUrgent();
                if (subscription.shouldSendNotification(isUrgent)) {
                    matched.add(subscription);
                }
            }
        }
        
        // 按优先级排序
        matched.sort((a, b) -> Integer.compare(b.getPriorityLevel(), a.getPriorityLevel()));
        
        return matched;
    }
    
    /**
     * 发送通知
     * 
     * @param subscription 订阅信息
     * @param alarm 告警对象
     * @return 通知结果
     */
    private NotificationResult sendNotification(AlarmSubscription subscription, Object alarm) {
        List<NotificationChannel> channels = subscription.getAvailableChannels();
        if (channels.isEmpty()) {
            return NotificationResult.failure(subscription.getSubscriptionId(), "没有可用的通知渠道");
        }
        
        List<String> successChannels = new ArrayList<>();
        List<String> failedChannels = new ArrayList<>();
        
        for (NotificationChannel channel : channels) {
            try {
                NotificationTemplate template = getTemplate(channel.getTemplateId(), channel.getChannelType());
                if (template == null) {
                    log.warn("未找到模板: {}", channel.getTemplateId());
                    failedChannels.add(channel.getChannelId());
                    continue;
                }
                
                // 准备模板上下文
                Map<String, Object> context = prepareTemplateContext(alarm, subscription);
                
                // 渲染模板
                NotificationTemplate.RenderedTemplate renderedTemplate = template.render(context);
                
                // 发送通知
                boolean sent = notificationSender.sendNotification(channel, renderedTemplate);
                
                if (sent) {
                    successChannels.add(channel.getChannelId());
                    channel.updateSendStatistics(true);
                } else {
                    failedChannels.add(channel.getChannelId());
                    channel.updateSendStatistics(false);
                }
                
            } catch (Exception e) {
                log.error("发送通知到渠道 {} 失败: {}", channel.getChannelId(), e.getMessage(), e);
                failedChannels.add(channel.getChannelId());
                channel.updateSendStatistics(false);
            }
        }
        
        boolean success = !successChannels.isEmpty();
        String message = String.format("成功: %d, 失败: %d", successChannels.size(), failedChannels.size());
        
        return new NotificationResult(subscription.getSubscriptionId(), success, message, 
                successChannels, failedChannels);
    }
    
    /**
     * 准备模板上下文
     * 
     * @param alarm 告警对象
     * @param subscription 订阅信息
     * @return 模板上下文
     */
    private Map<String, Object> prepareTemplateContext(Object alarm, AlarmSubscription subscription) {
        Map<String, Object> context = new ConcurrentHashMap<>();
        
        // 添加告警信息
        if (alarm instanceof Map<?, ?> alarmMap) {
            alarmMap.forEach((key, value) -> {
                if (key instanceof String) {
                    context.put((String) key, value);
                }
            });
        } else {
            // 使用反射提取告警对象的字段
            extractObjectFields(alarm, context);
        }
        
        // 添加订阅信息
        context.put("subscriptionId", subscription.getSubscriptionId());
        context.put("subscriptionName", subscription.getSubscriptionName());
        context.put("username", subscription.getUsername());
        
        // 添加时间信息
        context.put("notificationTime", LocalDateTime.now());
        
        return context;
    }
    
    /**
     * 提取对象字段到上下文
     */
    private void extractObjectFields(Object obj, Map<String, Object> context) {
        if (obj == null) {
            return;
        }
        
        try {
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(obj);
                context.put(field.getName(), value);
            }
        } catch (Exception e) {
            log.warn("提取对象字段失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取模板
     * 
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @return 模板实例
     */
    private NotificationTemplate getTemplate(String templateId, NotificationChannel.ChannelType channelType) {
        // 优先使用指定的模板ID
        if (templateId != null && !templateId.trim().isEmpty()) {
            NotificationTemplate template = templates.get(templateId);
            if (template != null && template.supportsChannelType(channelType)) {
                return template;
            }
        }
        
        // 使用默认模板
        String defaultTemplateId = "default_" + channelType.getCode();
        return templates.get(defaultTemplateId);
    }
    
    /**
     * 添加订阅
     * 
     * @param subscription 订阅信息
     */
    public void addSubscription(AlarmSubscription subscription) {
        if (subscription == null || !subscription.isValid()) {
            throw new IllegalArgumentException("无效的订阅信息");
        }
        
        subscriptions.put(subscription.getSubscriptionId(), subscription);
        
        // 更新用户订阅索引
        String userId = subscription.getUserId();
        userSubscriptionIndex.computeIfAbsent(userId, k -> new ArrayList<>())
                .add(subscription.getSubscriptionId());
        
        log.info("添加订阅: {}, 用户: {}", subscription.getSubscriptionId(), userId);
    }
    
    /**
     * 移除订阅
     * 
     * @param subscriptionId 订阅ID
     */
    public void removeSubscription(String subscriptionId) {
        AlarmSubscription removed = subscriptions.remove(subscriptionId);
        if (removed != null) {
            // 更新用户订阅索引
            String userId = removed.getUserId();
            List<String> userSubscriptions = userSubscriptionIndex.get(userId);
            if (userSubscriptions != null) {
                userSubscriptions.remove(subscriptionId);
                if (userSubscriptions.isEmpty()) {
                    userSubscriptionIndex.remove(userId);
                }
            }
            
            log.info("移除订阅: {}, 用户: {}", subscriptionId, userId);
        }
    }
    
    /**
     * 获取订阅
     * 
     * @param subscriptionId 订阅ID
     * @return 订阅信息
     */
    public AlarmSubscription getSubscription(String subscriptionId) {
        return subscriptions.get(subscriptionId);
    }
    
    /**
     * 获取用户的所有订阅
     * 
     * @param userId 用户ID
     * @return 订阅列表
     */
    public List<AlarmSubscription> getUserSubscriptions(String userId) {
        List<String> subscriptionIds = userSubscriptionIndex.get(userId);
        if (subscriptionIds == null || subscriptionIds.isEmpty()) {
            return List.of();
        }
        
        return subscriptionIds.stream()
                .map(subscriptions::get)
                .filter(java.util.Objects::nonNull)
                .toList();
    }
    
    /**
     * 添加模板
     * 
     * @param template 通知模板
     */
    public void addTemplate(NotificationTemplate template) {
        if (template == null || !template.isValid()) {
            throw new IllegalArgumentException("无效的模板信息");
        }
        
        templates.put(template.getTemplateId(), template);
        log.info("添加模板: {}", template.getTemplateId());
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public SubscriptionStatistics getStatistics() {
        return new SubscriptionStatistics(
                subscriptions.size(),
                userSubscriptionIndex.size(),
                templates.size(),
                totalProcessed.get(),
                totalMatched.get(),
                totalNotified.get(),
                totalFailed.get(),
                lastCleanupTime
        );
    }
    
    /**
     * 清理过期数据
     */
    public void cleanup() {
        // 这里可以实现清理逻辑，比如清理过期的统计数据等
        lastCleanupTime = LocalDateTime.now();
        log.debug("执行订阅管理器清理");
    }
    
    /**
     * 订阅处理结果
     */
    public static class SubscriptionProcessResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final boolean success;
        private final String message;
        private final int matchedCount;
        private final List<NotificationResult> notificationResults;
        
        private SubscriptionProcessResult(boolean success, String message, int matchedCount, 
                                        List<NotificationResult> notificationResults) {
            this.success = success;
            this.message = message;
            this.matchedCount = matchedCount;
            this.notificationResults = notificationResults != null ? notificationResults : List.of();
        }
        
        public static SubscriptionProcessResult success(int matchedCount, List<NotificationResult> results) {
            return new SubscriptionProcessResult(true, "处理成功", matchedCount, results);
        }
        
        public static SubscriptionProcessResult failure(String message) {
            return new SubscriptionProcessResult(false, message, 0, List.of());
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public int getMatchedCount() { return matchedCount; }
        public List<NotificationResult> getNotificationResults() { return notificationResults; }
    }
    
    /**
     * 通知结果
     */
    public static class NotificationResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String subscriptionId;
        private final boolean success;
        private final String message;
        private final List<String> successChannels;
        private final List<String> failedChannels;
        
        public NotificationResult(String subscriptionId, boolean success, String message,
                                List<String> successChannels, List<String> failedChannels) {
            this.subscriptionId = subscriptionId;
            this.success = success;
            this.message = message;
            this.successChannels = successChannels != null ? successChannels : List.of();
            this.failedChannels = failedChannels != null ? failedChannels : List.of();
        }
        
        public static NotificationResult success(String subscriptionId, String message) {
            return new NotificationResult(subscriptionId, true, message, List.of(), List.of());
        }
        
        public static NotificationResult failure(String subscriptionId, String message) {
            return new NotificationResult(subscriptionId, false, message, List.of(), List.of());
        }
        
        // Getters
        public String getSubscriptionId() { return subscriptionId; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public List<String> getSuccessChannels() { return successChannels; }
        public List<String> getFailedChannels() { return failedChannels; }
    }
    
    /**
     * 订阅统计信息
     */
    public record SubscriptionStatistics(
            int totalSubscriptions,
            int totalUsers,
            int totalTemplates,
            long totalProcessed,
            long totalMatched,
            long totalNotified,
            long totalFailed,
            LocalDateTime lastCleanupTime
    ) implements Serializable {
        
        public double getMatchRate() {
            if (totalProcessed == 0) {
                return 0.0;
            }
            return (double) totalMatched / totalProcessed;
        }
        
        public double getNotificationSuccessRate() {
            if (totalMatched == 0) {
                return 0.0;
            }
            return (double) totalNotified / totalMatched;
        }
    }
}
